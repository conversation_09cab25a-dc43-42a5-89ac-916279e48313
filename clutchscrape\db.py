from __future__ import annotations

import json
import logging
import sqlite3
from contextlib import closing
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Iterable, Optional

logger = logging.getLogger(__name__)


DEFAULT_DB_PATH = "clutchscrape.db"


@dataclass
class Agency:
    name: Optional[str]
    rating: Optional[float]
    review_count: Optional[int]
    location: Optional[str]
    description: Optional[str]
    contact_phone: Optional[str]
    contact_website: Optional[str]
    clutch_profile_url: Optional[str]
    data_json: Dict[str, Any]


class Database:
    def __init__(self, path: str | Path = DEFAULT_DB_PATH) -> None:
        self.path = str(path)
        Path(self.path).parent.mkdir(parents=True, exist_ok=True)
        self._ensure_schema()

    def _connect(self) -> sqlite3.Connection:
        conn = sqlite3.connect(self.path)
        conn.row_factory = sqlite3.Row
        return conn

    def _ensure_schema(self) -> None:
        with closing(self._connect()) as conn, conn:  # type: ignore
            conn.execute(
                """
                CREATE TABLE IF NOT EXISTS agencies (
                    id INTEGER PRIMARY KEY,
                    name TEXT,
                    rating REAL,
                    review_count INTEGER,
                    location TEXT,
                    description TEXT,
                    contact_phone TEXT,
                    contact_website TEXT,
                    clutch_profile_url TEXT UNIQUE,
                    data_json TEXT,
                    scraped_at TEXT
                )
                """
            )
            conn.execute(
                "CREATE INDEX IF NOT EXISTS idx_agencies_rating ON agencies(rating)"
            )
            conn.execute(
                "CREATE INDEX IF NOT EXISTS idx_agencies_location ON agencies(location)"
            )

    def upsert_agencies(self, agencies: Iterable[Agency]) -> int:
        count = 0
        with closing(self._connect()) as conn, conn:  # type: ignore
            for a in agencies:
                try:
                    conn.execute(
                        """
                        INSERT INTO agencies(
                            name, rating, review_count, location, description,
                            contact_phone, contact_website, clutch_profile_url,
                            data_json, scraped_at
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ON CONFLICT(clutch_profile_url) DO UPDATE SET
                            name=excluded.name,
                            rating=excluded.rating,
                            review_count=excluded.review_count,
                            location=excluded.location,
                            description=excluded.description,
                            contact_phone=excluded.contact_phone,
                            contact_website=excluded.contact_website,
                            data_json=excluded.data_json,
                            scraped_at=excluded.scraped_at
                        """,
                        (
                            a.name,
                            a.rating,
                            a.review_count,
                            a.location,
                            a.description,
                            a.contact_phone,
                            a.contact_website,
                            a.clutch_profile_url,
                            json.dumps(a.data_json, ensure_ascii=False),
                            datetime.utcnow().isoformat(timespec="seconds"),
                        ),
                    )
                    count += 1
                except sqlite3.Error as e:
                    logger.warning("Failed to upsert row for %s: %s", a.clutch_profile_url, e)
        return count

    def stats(self) -> Dict[str, Any]:
        with closing(self._connect()) as conn:
            row = conn.execute("SELECT COUNT(*) AS n FROM agencies").fetchone()
            totals = {"total": row[0] if row else 0}
            rating_dist = conn.execute(
                "SELECT CAST(rating AS INT) AS bucket, COUNT(*) AS n FROM agencies WHERE rating IS NOT NULL GROUP BY bucket ORDER BY bucket"
            ).fetchall()
            totals["ratings"] = {str(r["bucket"]): r["n"] for r in rating_dist}
            top_cities = conn.execute(
                "SELECT location, COUNT(*) AS n FROM agencies WHERE location IS NOT NULL AND TRIM(location) <> '' GROUP BY location ORDER BY n DESC LIMIT 10"
            ).fetchall()
            totals["top_locations"] = [{"location": r["location"], "count": r["n"]} for r in top_cities]
            return totals

    def all_rows(self) -> Iterable[sqlite3.Row]:
        with closing(self._connect()) as conn:
            for row in conn.execute("SELECT * FROM agencies ORDER BY rating DESC NULLS LAST, review_count DESC"):
                yield row


from __future__ import annotations

import argparse
import csv
import json
import logging
from pathlib import Path
from typing import Optional

from .db import Agency, Database, DEFAULT_DB_PATH
from .scraper import ClutchScraper, DEFAULT_URL, ScrapeOptions


def configure_logging(verbose: bool) -> None:
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format="%(asctime)s | %(levelname)s | %(name)s | %(message)s",
    )


def cmd_scrape(args: argparse.Namespace) -> int:
    configure_logging(args.verbose)

    db = Database(args.db)
    scraper = ClutchScraper()

    options = ScrapeOptions(
        delay_seconds=args.delay,
        max_pages=args.max_pages,
        min_rating=args.min_rating,
    )

    agencies = list(scraper.scrape(url=args.url, options=options))

    count = db.upsert_agencies(agencies)
    logging.info("Upserted %d agencies into %s", count, args.db)

    if args.export:
        export_path = Path(args.export)
        if export_path.suffix.lower() == ".json":
            with export_path.open("w", encoding="utf-8") as f:
                json.dump([a.__dict__ for a in agencies], f, ensure_ascii=False, indent=2)
            logging.info("Exported %d rows to %s", len(agencies), export_path)
        elif export_path.suffix.lower() == ".csv":
            with export_path.open("w", newline="", encoding="utf-8") as f:
                writer = csv.DictWriter(
                    f,
                    fieldnames=[
                        "name",
                        "rating",
                        "review_count",
                        "location",
                        "description",
                        "contact_phone",
                        "contact_website",
                        "clutch_profile_url",
                    ],
                )
                writer.writeheader()
                for a in agencies:
                    writer.writerow({
                        "name": a.name,
                        "rating": a.rating,
                        "review_count": a.review_count,
                        "location": a.location,
                        "description": a.description,
                        "contact_phone": a.contact_phone,
                        "contact_website": a.contact_website,
                        "clutch_profile_url": a.clutch_profile_url,
                    })
            logging.info("Exported %d rows to %s", len(agencies), export_path)
        else:
            logging.warning("Unsupported export extension: %s (use .csv or .json)", export_path.suffix)

    return 0


def cmd_stats(args: argparse.Namespace) -> int:
    configure_logging(args.verbose)
    db = Database(args.db)
    stats = db.stats()

    print(json.dumps(stats, indent=2))
    return 0


def build_parser() -> argparse.ArgumentParser:
    p = argparse.ArgumentParser(
        prog="clutch-scrape",
        description=(
            "Scrape Clutch.co recruitment agency listings and store results in SQLite.\n\n"
            "Examples:\n"
            "  python main.py scrape --max-pages 2 --delay 2.0\n"
            "  python main.py stats\n\n"
            "Dependencies: pip install requests beautifulsoup4\n"
        ),
        formatter_class=argparse.RawTextHelpFormatter,
    )

    p.add_argument("--db", default=DEFAULT_DB_PATH, help=f"SQLite DB path (default: {DEFAULT_DB_PATH})")
    p.add_argument("--verbose", action="store_true", help="Enable verbose logging")

    sub = p.add_subparsers(dest="command", required=True)

    sp = sub.add_parser("scrape", help="Run the scraper and store results in the database")
    sp.add_argument("--url", default=DEFAULT_URL, help="Start URL to scrape")
    sp.add_argument("--delay", type=float, default=1.5, help="Delay between page requests (seconds)")
    sp.add_argument("--max-pages", type=int, default=None, help="Maximum number of pages to scrape")
    sp.add_argument("--min-rating", type=float, default=None, help="Minimum rating filter (e.g., 4.5)")
    sp.add_argument("--export", default=None, help="Optional export path (.csv or .json)")
    sp.set_defaults(func=cmd_scrape)

    sp2 = sub.add_parser("stats", help="Show basic statistics about scraped data")
    sp2.set_defaults(func=cmd_stats)

    return p


def main(argv: Optional[list[str]] = None) -> int:
    parser = build_parser()
    args = parser.parse_args(argv)
    return args.func(args)


if __name__ == "__main__":  # pragma: no cover
    raise SystemExit(main())


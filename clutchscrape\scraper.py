from __future__ import annotations

import logging
import re
import time
from dataclasses import asdict, dataclass
from typing import Dict, Iterable, Iterator, List, Optional

import requests
from bs4 import BeautifulSoup  # type: ignore
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry  # type: ignore

from .db import Agency

logger = logging.getLogger(__name__)

DEFAULT_URL = (
    "https://clutch.co/ca/hr/recruiting?agency_size=Freelancer&agency_size=2+-+9&sort_by=ReviewRating"
)


@dataclass
class ScrapeOptions:
    delay_seconds: float = 1.5
    max_pages: Optional[int] = None
    min_rating: Optional[float] = None


class ClutchScraper:
    def __init__(self, user_agent: Optional[str] = None) -> None:
        self.session = requests.Session()
        retries = Retry(
            total=5,
            backoff_factor=0.6,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["GET"],
            raise_on_status=False,
        )
        adapter = HTTPAdapter(max_retries=retries)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        self.session.headers.update(
            {
                "User-Agent": user_agent
                or "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0 Safari/537.36",
                "Accept-Language": "en-US,en;q=0.9",
            }
        )

    def fetch(self, url: str) -> Optional[BeautifulSoup]:
        try:
            resp = self.session.get(url, timeout=30)
            if resp.status_code >= 400:
                logger.warning("HTTP %s for %s", resp.status_code, url)
                return None
            return BeautifulSoup(resp.text, "html.parser")
        except requests.RequestException as e:
            logger.error("Request failed for %s: %s", url, e)
            return None

    def parse_cards(self, soup: BeautifulSoup) -> Iterator[Agency]:
        # Try multiple selectors to be resilient to minor markup changes
        listings = soup.select("li.provider-row, div.provider-row, li.directory-listing, div.directory-listing")
        if not listings:
            # fallback: look for items with profile link
            listings = [el for el in soup.select("a[href^='/profile/']") if el.find_parent("li") or el.find_parent("div")]
            listings = [el.find_parent(["li", "div"]) for el in listings if el]
            listings = [el for el in listings if el]

        for card in listings:
            try:
                name_el = card.select_one("a[href^='/profile/']") or card.select_one("h3 a, h2 a")
                name = name_el.get_text(strip=True) if name_el else None
                profile_url = None
                if name_el and name_el.has_attr("href"):
                    href = name_el["href"].strip()
                    profile_url = "https://clutch.co" + href if href.startswith("/") else href

                rating = self._extract_rating(card)
                review_count = self._extract_review_count(card)
                location = self._extract_location(card)
                description = self._extract_description(card)
                phone = self._extract_phone(card)
                website = self._extract_website(card)

                data_json: Dict[str, object] = {
                    "raw_html_class": card.get("class", []),
                }

                yield Agency(
                    name=name,
                    rating=rating,
                    review_count=review_count,
                    location=location,
                    description=description,
                    contact_phone=phone,
                    contact_website=website,
                    clutch_profile_url=profile_url,
                    data_json=data_json,
                )
            except Exception as e:  # be resilient to individual parse errors
                logger.debug("Failed to parse a listing card: %s", e)
                continue

    def _extract_rating(self, card) -> Optional[float]:
        # Common patterns
        txt = None
        for sel in [
            "span.rating, span.clutch-average, span[itemprop='ratingValue']",
            "div.rating span",
            "[class*='rating']",
        ]:
            el = card.select_one(sel)
            if el and (val := self._to_float(el.get_text())) is not None:
                return val
            if el and not txt:
                txt = el.get_text(" ", strip=True)
        # Look for numbers like 4.8/5.0
        if not txt:
            txt = card.get_text(" ", strip=True)
        m = re.search(r"(\d+(?:\.\d)?)\s*/\s*5", txt or "")
        if m:
            return self._to_float(m.group(1))
        # Look for standalone X.X near 'rating'
        m2 = re.search(r"(\d+\.\d)(?=\s*(?:rating|stars))", (txt or "").lower())
        return self._to_float(m2.group(1)) if m2 else None

    def _extract_review_count(self, card) -> Optional[int]:
        txt = card.get_text(" ", strip=True)
        m = re.search(r"(\d{1,5})\+?\s+review", txt, re.I)
        return int(m.group(1)) if m else None

    def _extract_location(self, card) -> Optional[str]:
        for sel in [
            "span.locality, span.location, .list-item__address, .location",
            "[class*='location']",
        ]:
            el = card.select_one(sel)
            if el:
                return el.get_text(strip=True)
        return None

    def _extract_description(self, card) -> Optional[str]:
        for sel in [".module-list p, .description, .summary, p"]:
            el = card.select_one(sel)
            if el:
                text = el.get_text(" ", strip=True)
                if text and len(text.split()) >= 3:
                    return text
        return None

    def _extract_phone(self, card) -> Optional[str]:
        txt = card.get_text(" ", strip=True)
        m = re.search(r"(\+?\d[\d\s().-]{6,}\d)", txt)
        return m.group(1) if m else None

    def _extract_website(self, card) -> Optional[str]:
        for a in card.select("a"):
            href = a.get("href", "")
            label = a.get_text(strip=True).lower()
            if href.startswith("http") and ("website" in label or "visit" in label):
                return href
        return None

    def _to_float(self, s: Optional[str]) -> Optional[float]:
        if not s:
            return None
        try:
            return float(s.strip())
        except Exception:
            return None

    def next_page_url(self, soup: BeautifulSoup) -> Optional[str]:
        a = soup.select_one("a[rel='next'], li.next a, a.next")
        if a and a.has_attr("href"):
            href = a["href"].strip()
            return "https://clutch.co" + href if href.startswith("/") else href
        # Search for explicit '?page='
        for a in soup.select("a[href*='page=']"):
            if a.get_text(strip=True).lower() in {"next", ">", "›"}:
                href = a["href"].strip()
                return "https://clutch.co" + href if href.startswith("/") else href
        return None

    def scrape(self, url: str = DEFAULT_URL, options: Optional[ScrapeOptions] = None) -> Iterator[Agency]:
        opts = options or ScrapeOptions()
        page_count = 0
        next_url: Optional[str] = url

        while next_url:
            page_count += 1
            logger.info("Fetching page %s: %s", page_count, next_url)
            soup = self.fetch(next_url)
            if not soup:
                logger.warning("Skipping page due to fetch error: %s", next_url)
                break

            count_this_page = 0
            for agency in self.parse_cards(soup):
                if opts.min_rating is not None and (agency.rating or 0) < opts.min_rating:
                    continue
                yield agency
                count_this_page += 1

            logger.info("Parsed %d listings on page %d", count_this_page, page_count)

            if opts.max_pages and page_count >= opts.max_pages:
                break

            next_url = self.next_page_url(soup)
            if next_url:
                time.sleep(max(0.0, opts.delay_seconds))

